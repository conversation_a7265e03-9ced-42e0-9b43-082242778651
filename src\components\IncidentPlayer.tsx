'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, SkipB<PERSON>, Ski<PERSON>For<PERSON>, Volume2 } from 'lucide-react'
import { format } from 'date-fns'

interface Camera {
  id: string
  name: string
  location: string
}

interface Incident {
  id: string
  cameraId: string
  camera: Camera
  type: string
  tsStart: string
  tsEnd: string
  thumbnailUrl: string
  resolved: boolean
}

interface IncidentPlayerProps {
  incident: Incident | null
  otherCameras: Camera[]
}

export default function IncidentPlayer({ incident, otherCameras }: IncidentPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Mock video duration based on incident duration
  useEffect(() => {
    if (incident) {
      const start = new Date(incident.tsStart)
      const end = new Date(incident.tsEnd)
      const durationInSeconds = (end.getTime() - start.getTime()) / 1000
      setDuration(durationInSeconds)
      setCurrentTime(0)
    }
  }, [incident])

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const formatDateTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm:ss dd-MMM-yyyy')
  }

  if (!incident) {
    return (
      <div className="bg-gray-900 rounded-lg p-6 h-full flex items-center justify-center">
        <div className="text-center text-gray-400">
          <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <Play className="h-8 w-8" />
          </div>
          <p>Select an incident to view footage</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden">
      {/* Main Video Player */}
      <div className="relative aspect-video bg-black">
        {/* Placeholder video area */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-6xl mb-4">📹</div>
            <p className="text-lg font-semibold">{incident.camera.name}</p>
            <p className="text-sm text-gray-300">{incident.camera.location}</p>
          </div>
        </div>
        
        {/* Date/Time Overlay */}
        <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm font-mono">
          {formatDateTime(incident.tsStart)}
        </div>
        
        {/* Incident Type Badge */}
        <div className={`absolute top-4 right-4 px-3 py-1 rounded text-sm font-semibold ${
          incident.type === 'Gun Threat' ? 'bg-red-600 text-white' :
          incident.type === 'Unauthorized Access' ? 'bg-orange-600 text-white' :
          'bg-blue-600 text-white'
        }`}>
          {incident.type}
        </div>
      </div>

      {/* Video Controls */}
      <div className="p-4 bg-gray-800">
        <div className="flex items-center space-x-4">
          <button
            onClick={togglePlayPause}
            className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full transition-colors"
          >
            {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </button>
          
          <button className="text-gray-300 hover:text-white p-2">
            <SkipBack className="h-4 w-4" />
          </button>
          
          <button className="text-gray-300 hover:text-white p-2">
            <SkipForward className="h-4 w-4" />
          </button>
          
          <div className="flex-1 flex items-center space-x-2">
            <span className="text-sm text-gray-300 font-mono">{formatTime(currentTime)}</span>
            <div className="flex-1 bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
            <span className="text-sm text-gray-300 font-mono">{formatTime(duration)}</span>
          </div>
          
          <button className="text-gray-300 hover:text-white p-2">
            <Volume2 className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Other Camera Previews */}
      <div className="p-4 bg-gray-800 border-t border-gray-700">
        <h3 className="text-white text-sm font-semibold mb-3">Other Cameras</h3>
        <div className="grid grid-cols-2 gap-3">
          {otherCameras.slice(0, 2).map((camera) => (
            <div key={camera.id} className="bg-gray-700 rounded aspect-video flex items-center justify-center cursor-pointer hover:bg-gray-600 transition-colors">
              <div className="text-center text-gray-300">
                <div className="text-2xl mb-1">📹</div>
                <p className="text-xs font-medium">{camera.name}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
