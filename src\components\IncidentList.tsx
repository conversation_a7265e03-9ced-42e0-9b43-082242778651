'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import { AlertTriangle, Shield, Eye, Check, X } from 'lucide-react'

interface Camera {
  id: string
  name: string
  location: string
}

interface Incident {
  id: string
  cameraId: string
  camera: Camera
  type: string
  tsStart: string
  tsEnd: string
  thumbnailUrl: string
  resolved: boolean
}

interface IncidentListProps {
  incidents: Incident[]
  selectedIncident: Incident | null
  onSelectIncident: (incident: Incident) => void
  onResolveIncident: (incidentId: string) => void
}

export default function IncidentList({ 
  incidents, 
  selectedIncident, 
  onSelectIncident, 
  onResolveIncident 
}: IncidentListProps) {
  const [resolvingIds, setResolvingIds] = useState<Set<string>>(new Set())

  const getIncidentIcon = (type: string) => {
    switch (type) {
      case 'Gun Threat':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'Unauthorized Access':
        return <Shield className="h-5 w-5 text-orange-500" />
      case 'Face Recognised':
        return <Eye className="h-5 w-5 text-blue-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getIncidentColor = (type: string) => {
    switch (type) {
      case 'Gun Threat':
        return 'border-red-500'
      case 'Unauthorized Access':
        return 'border-orange-500'
      case 'Face Recognised':
        return 'border-blue-500'
      default:
        return 'border-gray-500'
    }
  }

  const handleResolve = async (incidentId: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent selecting the incident
    
    setResolvingIds(prev => new Set(prev).add(incidentId))
    
    try {
      await onResolveIncident(incidentId)
    } finally {
      setResolvingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(incidentId)
        return newSet
      })
    }
  }

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm')
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd MMM')
  }

  return (
    <div className="bg-white rounded-lg shadow-lg h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Incidents</h2>
        <p className="text-sm text-gray-600">
          {incidents.filter(i => !i.resolved).length} unresolved incidents
        </p>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {incidents.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-gray-400" />
            </div>
            <p>No incidents found</p>
          </div>
        ) : (
          <div className="space-y-2 p-2">
            {incidents.map((incident) => (
              <div
                key={incident.id}
                onClick={() => onSelectIncident(incident)}
                className={`
                  p-3 rounded-lg border-l-4 cursor-pointer transition-all duration-200
                  ${getIncidentColor(incident.type)}
                  ${selectedIncident?.id === incident.id 
                    ? 'bg-blue-50 shadow-md' 
                    : 'bg-gray-50 hover:bg-gray-100'
                  }
                  ${incident.resolved ? 'opacity-60' : ''}
                  ${resolvingIds.has(incident.id) ? 'animate-pulse' : ''}
                `}
              >
                <div className="flex items-start space-x-3">
                  {/* Thumbnail */}
                  <div className="flex-shrink-0">
                    <img
                      src={incident.thumbnailUrl}
                      alt={`${incident.type} thumbnail`}
                      className="w-16 h-10 object-cover rounded bg-gray-200"
                    />
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      {getIncidentIcon(incident.type)}
                      <span className="text-sm font-medium text-gray-900 truncate">
                        {incident.type}
                      </span>
                      {incident.resolved && (
                        <Check className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 truncate">
                      {incident.camera.name} • {incident.camera.location}
                    </p>
                    
                    <div className="flex items-center justify-between mt-2">
                      <div className="text-xs text-gray-500">
                        {formatTime(incident.tsStart)} - {formatTime(incident.tsEnd)}
                        <span className="ml-2">{formatDate(incident.tsStart)}</span>
                      </div>
                      
                      {!incident.resolved && (
                        <button
                          onClick={(e) => handleResolve(incident.id, e)}
                          disabled={resolvingIds.has(incident.id)}
                          className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50"
                        >
                          {resolvingIds.has(incident.id) ? 'Resolving...' : 'Resolve'}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
