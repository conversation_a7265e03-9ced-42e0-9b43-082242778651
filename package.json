{"name": "securesight", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.12.0", "@react-three/drei": "^10.5.2", "@react-three/fiber": "^9.2.0", "@types/three": "^0.178.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.4.2", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "sqlite3": "^5.1.7", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}