const fs = require('fs');
const path = require('path');

const thumbnailsDir = path.join(__dirname, '../public/thumbnails');

// Ensure directory exists
if (!fs.existsSync(thumbnailsDir)) {
  fs.mkdirSync(thumbnailsDir, { recursive: true });
}

const thumbnails = [
  { name: 'unauthorized-1.jpg', type: 'Unauthorized Access', color: '#ff4444' },
  { name: 'unauthorized-2.jpg', type: 'Unauthorized Access', color: '#ff4444' },
  { name: 'unauthorized-3.jpg', type: 'Unauthorized Access', color: '#ff4444' },
  { name: 'unauthorized-4.jpg', type: 'Unauthorized Access', color: '#ff4444' },
  { name: 'gun-threat-1.jpg', type: 'Gun Threat', color: '#ff0000' },
  { name: 'gun-threat-2.jpg', type: 'Gun Threat', color: '#ff0000' },
  { name: 'gun-threat-3.jpg', type: 'Gun Threat', color: '#ff0000' },
  { name: 'face-recognised-1.jpg', type: 'Face Recognised', color: '#4444ff' },
  { name: 'face-recognised-2.jpg', type: 'Face Recognised', color: '#4444ff' },
  { name: 'face-recognised-3.jpg', type: 'Face Recognised', color: '#4444ff' },
  { name: 'face-recognised-4.jpg', type: 'Face Recognised', color: '#4444ff' },
  { name: 'face-recognised-5.jpg', type: 'Face Recognised', color: '#4444ff' },
];

thumbnails.forEach(thumbnail => {
  const svgContent = `<svg width="160" height="90" xmlns="http://www.w3.org/2000/svg">
  <rect width="160" height="90" fill="${thumbnail.color}"/>
  <text x="80" y="40" text-anchor="middle" fill="white" font-family="Arial" font-size="10" font-weight="bold">${thumbnail.type}</text>
  <text x="80" y="55" text-anchor="middle" fill="white" font-family="Arial" font-size="8">Camera Feed</text>
</svg>`;
  
  const filePath = path.join(thumbnailsDir, thumbnail.name);
  fs.writeFileSync(filePath, svgContent);
  console.log(`Created ${thumbnail.name}`);
});

console.log('All thumbnail placeholders created!');
