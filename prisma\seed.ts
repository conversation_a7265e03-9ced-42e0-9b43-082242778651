import { PrismaClient } from '../src/generated/prisma'

const prisma = new PrismaClient()

async function main() {
  // Create cameras
  const cameras = await Promise.all([
    prisma.camera.create({
      data: {
        name: 'Shop Floor A',
        location: 'Main Production Area',
      },
    }),
    prisma.camera.create({
      data: {
        name: 'Vault',
        location: 'Security Vault - Level B1',
      },
    }),
    prisma.camera.create({
      data: {
        name: 'Entrance',
        location: 'Main Building Entrance',
      },
    }),
    prisma.camera.create({
      data: {
        name: 'Parking Lot',
        location: 'Employee Parking Area',
      },
    }),
  ])

  // Create incidents across the last 24 hours
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  const incidents = [
    // Unauthorized Access incidents
    {
      cameraId: cameras[0].id,
      type: 'Unauthorized Access',
      tsStart: new Date(yesterday.getTime() + 2 * 60 * 60 * 1000), // 2 hours after yesterday
      tsEnd: new Date(yesterday.getTime() + 2 * 60 * 60 * 1000 + 3 * 60 * 1000), // 3 minutes later
      thumbnailUrl: '/thumbnails/unauthorized-1.jpg',
      resolved: false,
    },
    {
      cameraId: cameras[2].id,
      type: 'Unauthorized Access',
      tsStart: new Date(yesterday.getTime() + 8 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 8 * 60 * 60 * 1000 + 5 * 60 * 1000),
      thumbnailUrl: '/thumbnails/unauthorized-2.jpg',
      resolved: true,
    },
    {
      cameraId: cameras[3].id,
      type: 'Unauthorized Access',
      tsStart: new Date(yesterday.getTime() + 14 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 14 * 60 * 60 * 1000 + 2 * 60 * 1000),
      thumbnailUrl: '/thumbnails/unauthorized-3.jpg',
      resolved: false,
    },
    {
      cameraId: cameras[0].id,
      type: 'Unauthorized Access',
      tsStart: new Date(yesterday.getTime() + 20 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 20 * 60 * 60 * 1000 + 4 * 60 * 1000),
      thumbnailUrl: '/thumbnails/unauthorized-4.jpg',
      resolved: false,
    },

    // Gun Threat incidents
    {
      cameraId: cameras[1].id,
      type: 'Gun Threat',
      tsStart: new Date(yesterday.getTime() + 6 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 6 * 60 * 60 * 1000 + 1 * 60 * 1000),
      thumbnailUrl: '/thumbnails/gun-threat-1.jpg',
      resolved: true,
    },
    {
      cameraId: cameras[2].id,
      type: 'Gun Threat',
      tsStart: new Date(yesterday.getTime() + 12 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 12 * 60 * 60 * 1000 + 2 * 60 * 1000),
      thumbnailUrl: '/thumbnails/gun-threat-2.jpg',
      resolved: false,
    },
    {
      cameraId: cameras[3].id,
      type: 'Gun Threat',
      tsStart: new Date(yesterday.getTime() + 18 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 18 * 60 * 60 * 1000 + 3 * 60 * 1000),
      thumbnailUrl: '/thumbnails/gun-threat-3.jpg',
      resolved: false,
    },

    // Face Recognised incidents
    {
      cameraId: cameras[0].id,
      type: 'Face Recognised',
      tsStart: new Date(yesterday.getTime() + 4 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 4 * 60 * 60 * 1000 + 30 * 1000),
      thumbnailUrl: '/thumbnails/face-recognised-1.jpg',
      resolved: true,
    },
    {
      cameraId: cameras[1].id,
      type: 'Face Recognised',
      tsStart: new Date(yesterday.getTime() + 10 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 10 * 60 * 60 * 1000 + 45 * 1000),
      thumbnailUrl: '/thumbnails/face-recognised-2.jpg',
      resolved: true,
    },
    {
      cameraId: cameras[2].id,
      type: 'Face Recognised',
      tsStart: new Date(yesterday.getTime() + 16 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 16 * 60 * 60 * 1000 + 1 * 60 * 1000),
      thumbnailUrl: '/thumbnails/face-recognised-3.jpg',
      resolved: false,
    },
    {
      cameraId: cameras[3].id,
      type: 'Face Recognised',
      tsStart: new Date(yesterday.getTime() + 22 * 60 * 60 * 1000),
      tsEnd: new Date(yesterday.getTime() + 22 * 60 * 60 * 1000 + 20 * 1000),
      thumbnailUrl: '/thumbnails/face-recognised-4.jpg',
      resolved: false,
    },
    {
      cameraId: cameras[0].id,
      type: 'Face Recognised',
      tsStart: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
      tsEnd: new Date(now.getTime() - 2 * 60 * 60 * 1000 + 1 * 60 * 1000),
      thumbnailUrl: '/thumbnails/face-recognised-5.jpg',
      resolved: false,
    },
  ]

  // Create all incidents
  for (const incident of incidents) {
    await prisma.incident.create({
      data: incident,
    })
  }

  console.log('Database seeded successfully!')
  console.log(`Created ${cameras.length} cameras and ${incidents.length} incidents`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
