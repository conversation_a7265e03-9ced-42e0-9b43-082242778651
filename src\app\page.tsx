'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import IncidentPlayer from '@/components/IncidentPlayer'
import IncidentList from '@/components/IncidentList'

interface Camera {
  id: string
  name: string
  location: string
}

interface Incident {
  id: string
  cameraId: string
  camera: Camera
  type: string
  tsStart: string
  tsEnd: string
  thumbnailUrl: string
  resolved: boolean
}

export default function Dashboard() {
  const [incidents, setIncidents] = useState<Incident[]>([])
  const [cameras, setCameras] = useState<Camera[]>([])
  const [selectedIncident, setSelectedIncident] = useState<Incident | null>(null)
  const [loading, setLoading] = useState(true)
  const [showResolved, setShowResolved] = useState(false)

  useEffect(() => {
    fetchIncidents()
  }, [showResolved])

  const fetchIncidents = async () => {
    try {
      const response = await fetch(`/api/incidents${showResolved ? '' : '?resolved=false'}`)
      const data = await response.json()
      setIncidents(data)

      // Extract unique cameras
      const uniqueCameras = data.reduce((acc: Camera[], incident: Incident) => {
        if (!acc.find(cam => cam.id === incident.camera.id)) {
          acc.push(incident.camera)
        }
        return acc
      }, [])
      setCameras(uniqueCameras)

      // Select first unresolved incident by default
      if (!selectedIncident && data.length > 0) {
        const firstUnresolved = data.find((incident: Incident) => !incident.resolved)
        setSelectedIncident(firstUnresolved || data[0])
      }
    } catch (error) {
      console.error('Error fetching incidents:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleResolveIncident = async (incidentId: string) => {
    try {
      const response = await fetch(`/api/incidents/${incidentId}/resolve`, {
        method: 'PATCH',
      })

      if (response.ok) {
        // Optimistically update the UI
        setIncidents(prev =>
          prev.map(incident =>
            incident.id === incidentId
              ? { ...incident, resolved: true }
              : incident
          )
        )

        // If we're not showing resolved incidents, remove it from the list
        if (!showResolved) {
          setIncidents(prev => prev.filter(incident => incident.id !== incidentId))

          // If the resolved incident was selected, select another one
          if (selectedIncident?.id === incidentId) {
            const remainingIncidents = incidents.filter(i => i.id !== incidentId && !i.resolved)
            setSelectedIncident(remainingIncidents.length > 0 ? remainingIncidents[0] : null)
          }
        }
      }
    } catch (error) {
      console.error('Error resolving incident:', error)
    }
  }

  const otherCameras = cameras.filter(camera => camera.id !== selectedIncident?.cameraId)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Navbar />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading incidents...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar />

      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Security Dashboard</h1>
          <div className="flex items-center space-x-4">
            <p className="text-gray-600">
              Monitor and review CCTV incidents across all camera feeds
            </p>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showResolved}
                onChange={(e) => setShowResolved(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">Show resolved incidents</span>
            </label>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Left Panel - Incident Player */}
          <div className="lg:col-span-2">
            <IncidentPlayer
              incident={selectedIncident}
              otherCameras={otherCameras}
            />
          </div>

          {/* Right Panel - Incident List */}
          <div className="lg:col-span-1">
            <IncidentList
              incidents={incidents}
              selectedIncident={selectedIncident}
              onSelectIncident={setSelectedIncident}
              onResolveIncident={handleResolveIncident}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
